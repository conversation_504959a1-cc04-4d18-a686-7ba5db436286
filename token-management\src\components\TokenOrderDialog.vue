<template>
  <el-dialog
    v-model="dialogVisible"
    title="新Token置入账号内订单"
    width="500px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="token-order-content">
      <div class="info-section">
        <p>您正要将选中的Token置入账号内订单</p>
        <div class="selected-tokens-info">
          已选择 <el-tag type="primary">{{ selectedTokensCount }}</el-tag> 个Token
        </div>

        <!-- 按用户名分组显示统计 -->
        <div class="user-stats-section" v-if="selectedTokensCount > 0">
          <div class="user-stats-header">
            按用户分组统计:
            <span v-if="userCount > 5" class="expand-toggle" @click="expandUserList = !expandUserList">
              {{ expandUserList ? '收起' : '展开' }}
            </span>
            </div>
          <div class="user-stats-list">
            <div v-for="(count, user) in displayedUsers" :key="user" class="user-stats-item">
              <div class="user-name" @click="showUserProgressDetails(user)" style="cursor: pointer">
                <span class="label">用户:</span>
                <el-tag size="small" effect="plain" type="success">{{ user }}</el-tag>
                <el-icon v-if="processing || completed"><el-icon-view /></el-icon>
            </div>
              <div class="user-count">
                <span class="label">数量:</span>
                <el-tag size="small" effect="dark" type="primary">{{ count }}</el-tag>
          </div>
              <!-- 用户处理进度 -->
              <div v-if="(processing || completed) && userProgress[user]" class="user-progress">
                <el-progress
                  :percentage="userProgress[user].percent"
                  :format="() => `${userProgress[user].processed}/${userProgress[user].total}`"
                  :status="userProgress[user].processing ? '' : (userProgress[user].success ? 'success' : 'exception')"
                  size="small"
                  style="width: 150px"
                />
              </div>
            </div>
          </div>
          <div v-if="userCount > 5 && !expandUserList" class="collapsed-info">
            <el-tag type="info" size="small">另外 {{ userCount - 5 }} 个用户已折叠</el-tag>
          </div>
        </div>

        <!-- 并发设置区域 -->
        <div class="concurrent-settings" v-if="selectedTokensCount > 0">
          <div class="settings-title">并发设置:</div>
          <div class="settings-form">
            <div class="setting-item">
              <span class="setting-label">每批Token数量:</span>
              <el-input-number v-model="batchSize" :min="1" :max="500" size="small" />
            </div>
            <div class="setting-item">
              <span class="setting-label">最大并发请求数:</span>
              <el-input-number v-model="maxConcurrent" :min="1" :max="100" size="small" />
            </div>
          </div>
        </div>

        <!-- 自动删除订单设置区域 -->
        <div class="auto-delete-settings" v-if="selectedTokensCount > 0">
          <div class="settings-title">自动删除订单:</div>
          <div class="auto-delete-form">
            <div class="auto-delete-item">
              <el-checkbox v-model="autoDeleteOrders" size="large">
                <span class="checkbox-label">置入结束后自动删除订单</span>
              </el-checkbox>
              <div class="auto-delete-description" v-if="autoDeleteOrders">
                <el-text type="info" size="small">
                  将自动查询并删除可删除状态的订单（已评价、交易成功、交易已取消、未发货、退款成功）
                </el-text>
              </div>
            </div>
          </div>
        </div>

        <!-- 处理进度显示 -->
        <div v-if="processing" class="progress-container">
          <div class="progress-title">正在处理: {{ progressText }}</div>
          <el-progress :percentage="progress" :format="() => `${processedBatches}/${totalBatches} 批次`" />
          <div class="progress-details" v-if="lastBatchResult">
            <div class="batch-info">
              <span class="label">当前批次:</span>
              <span class="value">{{ lastBatchResult.username }} ({{ lastBatchResult.successCount }}/{{ lastBatchResult.totalCount }})</span>
            </div>
          </div>
        </div>

        <!-- 删除订单进度显示 -->
        <div v-if="deletingOrders" class="delete-progress-container">
          <div class="delete-progress-title">正在删除订单: {{ deleteProgressText }}</div>
          <el-progress
            :percentage="deleteProgress"
            :format="() => `${processedDeleteTokens}/${totalDeleteTokens} 个Token`"
            status="warning"
          />
          <div class="delete-progress-details" v-if="lastDeleteResult">
            <div class="delete-info">
              <span class="label">当前Token:</span>
              <span class="value">{{ lastDeleteResult.username }} (成功: {{ lastDeleteResult.successCount }}, 失败: {{ lastDeleteResult.failCount }})</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="dialog-footer">
      <el-button @click="handleClose" :disabled="processing || deletingOrders">取消</el-button>
      <el-button type="primary" :loading="loading || processing || deletingOrders" @click="handleSubmit">
        {{ deletingOrders ? '删除订单中...' : (processing ? '处理中...' : '提交') }}
      </el-button>
    </div>

    <!-- 操作结果对话框 -->
    <el-dialog
      v-model="resultDialogVisible"
      :title="resultDialogTitle"
      width="500px"
      append-to-body
    >
      <div class="result-content">
        <div class="result-icon-container">
          <el-icon class="result-icon" :class="operationSuccess ? 'success-icon' : 'error-icon'">
            <el-icon-circle-check v-if="operationSuccess" />
            <el-icon-circle-close v-else />
          </el-icon>
      </div>

        <p class="result-message">{{ resultMessage }}</p>

        <!-- 操作统计信息 -->
        <div class="result-statistics">
          <div class="stat-section">
            <div class="stat-section-title">Token置入订单</div>
            <div class="stat-cards">
              <div class="stat-card">
                <div class="stat-value total">{{ totalCount }}</div>
                <div class="stat-label">总数量</div>
              </div>
              <div class="stat-card">
                <div class="stat-value success">{{ successCount }}</div>
                <div class="stat-label">成功</div>
              </div>
              <div class="stat-card">
                <div class="stat-value fail">{{ failCount }}</div>
                <div class="stat-label">失败</div>
              </div>
            </div>
          </div>

          <!-- 删除订单统计 -->
          <div class="stat-section" v-if="autoDeleteOrders && deleteTotalCount > 0">
            <div class="stat-section-title">自动删除订单</div>
            <div class="stat-cards">
              <div class="stat-card">
                <div class="stat-value total">{{ deleteTotalCount }}</div>
                <div class="stat-label">总订单数</div>
              </div>
              <div class="stat-card">
                <div class="stat-value success">{{ deleteSuccessCount }}</div>
                <div class="stat-label">删除成功</div>
              </div>
              <div class="stat-card">
                <div class="stat-value fail">{{ deleteFailCount }}</div>
                <div class="stat-label">删除失败</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 时间统计信息 -->
        <div class="time-statistics">
          <div class="time-item">
            <span class="time-label">开始时间:</span>
            <span class="time-value">{{ formatDateTime(startTime) }}</span>
          </div>
          <div class="time-item">
            <span class="time-label">结束时间:</span>
            <span class="time-value">{{ formatDateTime(endTime) }}</span>
          </div>
          <div class="time-item total-time">
            <span class="time-label">总用时:</span>
            <span class="time-value">{{ formatDuration(totalDuration) }}</span>
          </div>
        </div>

        <!-- 批次处理统计 -->
        <div class="batch-stats-summary">
          <div class="summary-title">批次处理统计:</div>
          <div class="summary-content">总批次数: {{ totalBatches }} | 每批大小: {{ batchSize }} | 并发数: {{ maxConcurrent }}</div>
        </div>

        <!-- 按用户名分组的操作结果统计 -->
        <div class="user-results-section" v-if="Object.keys(resultsByUser).length > 0">
          <div class="section-title">用户处理统计:</div>
          <div class="user-results-list">
            <div v-for="(results, user) in resultsByUser" :key="user" class="user-result-item">
              <div class="user-result-header">
                <div class="user-result-name">
                  <el-tag size="small" effect="plain" type="success" @click="showUserDetails(user)" class="clickable-tag">{{ user }}</el-tag>
                </div>
              </div>
              <div class="user-result-stats">
                <div class="user-result-stat success">
                  <span class="stat-label">成功:</span>
                  <span class="stat-value">{{ results.success }}</span>
                </div>
                <div class="user-result-stat total">
                  <span class="stat-label">总量:</span>
                  <span class="stat-value">{{ results.total }}</span>
                </div>
                <div class="user-result-stat fail">
                  <span class="stat-label">失败:</span>
                  <span class="stat-value">{{ results.fail }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleResultDialogClose">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 请求详情对话框 -->
    <el-dialog
      v-model="detailsDialogVisible"
      :title="`用户 ${selectedUser} 的原始请求和响应数据`"
      width="700px"
      append-to-body
    >
      <div class="details-content">
        <el-tabs type="border-card">
          <el-tab-pane label="原始请求数据">
            <h3>发送到服务器的原始请求数据</h3>
            <pre class="json-code">{{ formattedRequestData }}</pre>
          </el-tab-pane>
          <el-tab-pane label="原始响应数据">
            <h3>服务器返回的原始响应数据</h3>
            <pre class="json-code">{{ formattedResponseData }}</pre>
          </el-tab-pane>
          <el-tab-pane label="批次明细" v-if="concurrentMode">
            <h3>批次处理明细 ({{ userBatchesCount }} 个批次)</h3>
            <div class="batch-details-list">
              <div v-for="(batch, index) in userBatches" :key="index" class="batch-detail-item">
                <div class="batch-header">
                  <div class="batch-title">批次 #{{ batch.batchIndex + 1 }}</div>
                  <el-tag :type="batch.success ? 'success' : 'danger'" size="small">
                    {{ batch.success ? '成功' : '失败' }}
                  </el-tag>
                </div>
                <div class="batch-stats">
                  <div class="batch-stat">处理数量: {{ batch.totalCount }}</div>
                  <div class="batch-stat">成功: {{ batch.successCount }}</div>
                  <div class="batch-stat">失败: {{ batch.failCount }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="detailsDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 用户进度详情对话框 -->
    <el-dialog
      v-model="userProgressDialogVisible"
      :title="`用户 ${selectedUser} 的处理进度`"
      width="600px"
      append-to-body
    >
      <div class="progress-details-content">
        <div class="progress-overview">
          <div class="progress-stat">
            <div class="stat-label">总批次:</div>
            <div class="stat-value">{{ selectedUserProgress?.total || 0 }}</div>
          </div>
          <div class="progress-stat">
            <div class="stat-label">已处理:</div>
            <div class="stat-value">{{ selectedUserProgress?.processed || 0 }}</div>
          </div>
          <div class="progress-stat">
            <div class="stat-label">成功:</div>
            <div class="stat-value success">{{ selectedUserProgress?.successCount || 0 }}</div>
          </div>
          <div class="progress-stat">
            <div class="stat-label">失败:</div>
            <div class="stat-value fail">{{ selectedUserProgress?.failCount || 0 }}</div>
          </div>
        </div>

        <div class="progress-bar-container">
          <el-progress
            :percentage="selectedUserProgress?.percent || 0"
            :status="selectedUserProgress?.processing ? '' : (selectedUserProgress?.success ? 'success' : 'exception')"
          />
        </div>

        <div class="batch-list-title">批次详情:</div>
        <div class="user-batch-list">
          <div v-for="(batch, index) in selectedUserBatches" :key="index" class="user-batch-item" @click="showBatchDetails(batch)">
            <div class="batch-header">
              <span class="batch-name">批次 #{{ batch.batchIndex + 1 }}</span>
              <el-tag size="small" :type="batch.processed ? (batch.success ? 'success' : 'danger') : 'info'">
                {{ batch.processed ? (batch.success ? '成功' : '失败') : '处理中...' }}
              </el-tag>
            </div>
            <div class="batch-details">
              <div class="batch-detail-item">
                <span class="detail-label">处理数量:</span>
                <span class="detail-value">{{ batch.totalCount || 0 }}</span>
              </div>
              <div class="batch-detail-item">
                <span class="detail-label">成功:</span>
                <span class="detail-value success">{{ batch.successCount || 0 }}</span>
              </div>
              <div class="batch-detail-item">
                <span class="detail-label">失败:</span>
                <span class="detail-value fail">{{ batch.failCount || 0 }}</span>
              </div>
            </div>
            <div class="batch-status" v-if="!batch.processed">
              <div class="processing-animation">
                <span></span><span></span><span></span>
              </div>
              <div class="processing-text">批次处理中...</div>
            </div>
          </div>
          <div v-if="selectedUserBatches.length === 0" class="no-batch-data">
            <el-empty description="暂无批次数据" :image-size="100"></el-empty>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 批次详情对话框 -->
    <el-dialog
      v-model="batchDetailsDialogVisible"
      :title="`批次 #${selectedBatch?.batchIndex + 1} 详情`"
      width="700px"
      append-to-body
    >
      <div class="batch-details-content">
        <div class="batch-overview">
          <div class="batch-info-item">
            <span class="batch-info-label">用户:</span>
            <span class="batch-info-value">{{ selectedBatch?.username }}</span>
          </div>
          <div class="batch-info-item">
            <span class="batch-info-label">状态:</span>
            <el-tag size="small" :type="selectedBatch?.success ? 'success' : 'danger'">
              {{ selectedBatch?.success ? '成功' : '失败' }}
            </el-tag>
          </div>
          <div class="batch-info-item">
            <span class="batch-info-label">处理数量:</span>
            <span class="batch-info-value">{{ selectedBatch?.totalCount }}</span>
          </div>
          <div class="batch-info-item">
            <span class="batch-info-label">成功:</span>
            <span class="batch-info-value success">{{ selectedBatch?.successCount }}</span>
          </div>
          <div class="batch-info-item">
            <span class="batch-info-label">失败:</span>
            <span class="batch-info-value fail">{{ selectedBatch?.failCount }}</span>
          </div>
        </div>

        <el-tabs type="border-card" class="batch-details-tabs">
          <el-tab-pane label="请求数据">
            <div class="batch-data-section">
              <h3>发送到服务器的请求数据</h3>
              <pre class="json-code">{{ formattedBatchRequestData }}</pre>
            </div>
          </el-tab-pane>
          <el-tab-pane label="响应数据">
            <div class="batch-data-section">
              <h3>服务器返回的响应数据</h3>
              <pre class="json-code">{{ formattedBatchResponseData }}</pre>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { placeOrder, addTokensToOrderConcurrent } from '@/services/tokenOrderService';
import { autoQueryAndDeleteOrders } from '@/services/orderService';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedTokens: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'success', 'error']);

// 计算属性，绑定到props
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 计算选中的Token数量
const selectedTokensCount = computed(() => props.selectedTokens.length);

// 显示前5个Token的预览
const previewTokens = computed(() => {
  return props.selectedTokens.slice(0, 5);
});

// 按用户名分组统计Token数量
const tokensByUser = computed(() => {
  const userStats = {};
  props.selectedTokens.forEach(token => {
    const user = token.user || '未知用户';
    if (!userStats[user]) {
      userStats[user] = 0;
    }
    userStats[user]++;
  });
  return userStats;
});

// 并发设置
const concurrentMode = ref(true); // 默认启用并发模式
const batchSize = ref(50); // 降低每批处理的Token数量
const maxConcurrent = ref(20); // 减少最大并发请求数

// 进度跟踪
const processing = ref(false); // 正在处理标志
const progress = ref(0); // 进度百分比
const progressText = ref(''); // 进度文本描述
const processedBatches = ref(0); // 已处理批次数
const totalBatches = ref(0); // 总批次数
const lastBatchResult = ref(null); // 最后一批处理结果

// 加载状态
const loading = ref(false);

// 进度完成状态 - 新增
const completed = ref(false);

// 自动删除订单相关状态
const autoDeleteOrders = ref(true); // 默认勾选
const deletingOrders = ref(false); // 正在删除订单标志
const deleteProgress = ref(0); // 删除进度百分比
const deleteProgressText = ref(''); // 删除进度文本
const processedDeleteTokens = ref(0); // 已处理的删除Token数
const totalDeleteTokens = ref(0); // 总删除Token数
const lastDeleteResult = ref(null); // 最后一个删除结果

// 结果对话框状态
const resultDialogVisible = ref(false);
const resultDialogTitle = ref('操作结果');
const resultMessage = ref('');
const operationSuccess = ref(false);

// 统计数据
const successCount = ref(0);
const failCount = ref(0);
const totalCount = ref(0);

// 删除订单统计数据
const deleteSuccessCount = ref(0);
const deleteFailCount = ref(0);
const deleteTotalCount = ref(0);

// 按用户名分组的操作结果统计
const resultsByUser = ref({});

// 请求和响应数据
const requestData = ref(null);
const responseData = ref(null);
const batchResults = ref([]); // 存储所有批次的处理结果

// 详情对话框状态
const detailsDialogVisible = ref(false);
const selectedUser = ref('');

// 用户列表折叠状态
const expandUserList = ref(false);

// 用户总数
const userCount = computed(() => Object.keys(tokensByUser.value).length);

// 根据折叠状态显示的用户列表
const displayedUsers = computed(() => {
  const users = Object.entries(tokensByUser.value);

  if (users.length <= 5 || expandUserList.value) {
    return tokensByUser.value;
  }

  // 只显示前5个用户
  return Object.fromEntries(users.slice(0, 5));
});

// 用户进度跟踪
const userProgress = ref({});
const userProgressDialogVisible = ref(false);
const selectedUserBatches = ref([]);

// 计算当前选中用户的进度
const selectedUserProgress = computed(() => {
  if (!selectedUser.value || !userProgress.value[selectedUser.value]) {
    return null;
  }
  return userProgress.value[selectedUser.value];
});

// 显示用户进度详情
const showUserProgressDetails = (user) => {
  selectedUser.value = user;
  userProgressDialogVisible.value = true;

  console.log('显示用户进度详情:', user);
  console.log('所有批次数据:', batchResults.value);

  // 过滤出该用户的批次
  const userBatches = batchResults.value.filter(batch => batch.username === user);
  console.log('过滤后的用户批次:', userBatches);

  // 确保按批次索引排序
  userBatches.sort((a, b) => a.batchIndex - b.batchIndex);

  // 确保每个批次都有基本信息
  const enrichedBatches = userBatches.map(batch => ({
    batchIndex: batch.batchIndex || 0,
    username: batch.username || user,
    processed: !!batch.processed,
    success: !!batch.success,
    totalCount: batch.totalCount || (batch.tokens ? batch.tokens.length : 0),
    successCount: batch.successCount || 0,
    failCount: batch.failCount || 0,
    tokens: batch.tokens || [],
    requestData: batch.requestData || null,
    responseData: batch.responseData || null
  }));

  console.log('增强后的批次数据:', enrichedBatches);
  selectedUserBatches.value = enrichedBatches;

  // 如果没有找到批次数据
  if (userBatches.length === 0) {
    console.warn(`未找到用户 ${user} 的批次数据`);
    ElMessage.info(`未找到用户 ${user} 的批次数据`);
  }
};

// 处理进度回调 - 修改以支持用户级别进度
const handleProgress = (progressData) => {
  processedBatches.value = progressData.processed;
  totalBatches.value = progressData.total;
  progress.value = progressData.percent;
  progressText.value = `${progressData.percent}% 完成`;

  if (progressData.currentBatch) {
    lastBatchResult.value = progressData.currentBatch;

    // 更新用户级别的进度
    const username = progressData.currentBatch.username;

    // 如果是该用户的首次处理，初始化进度
    if (!userProgress.value[username]) {
      // 计算该用户的总批次数
      const userBatchCount = batches.value.filter(batch => batch.username === username).length;

      userProgress.value[username] = {
        total: userBatchCount,
        processed: 0,
        percent: 0,
        processing: true,
        successCount: 0,
        failCount: 0
      };
    }

    // 更新该用户的批次处理进度
    const currentBatch = progressData.currentBatch;
    userProgress.value[username].processed++;
    userProgress.value[username].percent = Math.round((userProgress.value[username].processed / userProgress.value[username].total) * 100);

    // 更新成功/失败计数
    userProgress.value[username].successCount += currentBatch.successCount || 0;
    userProgress.value[username].failCount += currentBatch.failCount || 0;

    // 如果用户的所有批次都处理完毕，标记完成
    if (userProgress.value[username].processed === userProgress.value[username].total) {
      userProgress.value[username].processing = false;
      userProgress.value[username].success = userProgress.value[username].successCount > 0;
    }

    // 更新批次结果数组
    const batchIndex = batchResults.value.findIndex(batch =>
      batch.batchIndex === currentBatch.batchIndex && batch.username === username
    );

    if (batchIndex >= 0) {
      // 更新已存在的批次
      batchResults.value[batchIndex] = {
        ...batchResults.value[batchIndex],
        ...currentBatch,
        processed: true
      };
    } else {
      // 添加新批次
      batchResults.value.push({
        ...currentBatch,
        processed: true
      });
    }

    // 确保selectedUserBatches同步更新
    if (selectedUser.value === username) {
      selectedUserBatches.value = batchResults.value.filter(
        batch => batch.username === username
      );
    }
  }
};

// 操作时间跟踪
const startTime = ref(null);
const endTime = ref(null);
const totalDuration = ref(0);

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '无记录';

  const date = new Date(dateTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

// 格式化持续时间
const formatDuration = (ms) => {
  if (!ms) return '0秒';

  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes > 0) {
    return `${minutes}分${remainingSeconds}秒`;
  }
  return `${seconds}秒`;
};

// 处理提交 - 支持单一请求和并发请求
const handleSubmit = async () => {
  // 验证选中的Token
  if (props.selectedTokens.length === 0) {
    ElMessage.warning('请选择至少一个Token');
    return;
  }

  // 显示加载状态
  loading.value = true;

  // 记录开始时间
  startTime.value = Date.now();
  endTime.value = null;
  totalDuration.value = 0;

  // 重置进度状态
  processing.value = true;
  completed.value = false;
  progress.value = 0;
  progressText.value = '准备中...';
  processedBatches.value = 0;
  totalBatches.value = 0;

  // 重置进度数据
  batchResults.value = [];
  userProgress.value = {};

  // 准备批次数据
  batches.value = prepareBatches(props.selectedTokens, batchSize.value);

  // 初始化每个用户的批次记录
  Object.keys(tokensByUser.value).forEach(username => {
    const userBatches = batches.value.filter(batch => batch.username === username);

    // 为每个批次创建占位
    userBatches.forEach(batch => {
      batchResults.value.push({
        batchIndex: batch.batchIndex,
        username: batch.username,
        processed: false,
        success: false,
        tokens: batch.tokens,
        totalCount: batch.tokens.length,
        successCount: 0,
        failCount: 0
      });
    });

    // 初始化用户进度
    userProgress.value[username] = {
      total: userBatches.length,
      processed: 0,
      percent: 0,
      processing: true,
      successCount: 0,
      failCount: 0
    };
  });

  try {
    let result;

    // 使用并发模式处理
    if (concurrentMode.value) {
      result = await addTokensToOrderConcurrent(props.selectedTokens, {
        batchSize: batchSize.value,
        maxConcurrent: maxConcurrent.value,
        onProgress: handleProgress,
        retryCount: 2,
        retryDelay: 1000
      });

      // 存储批次结果
      batchResults.value = result.batchResults || [];

      // 合并请求和响应数据以便于详情查看
      requestData.value = result.requestsData || [];
      responseData.value = result.responsesData || [];
    } else {
      // 使用单一请求方式
      result = await placeOrder(props.selectedTokens);

      // 保存请求和响应数据
      requestData.value = {
        tokens: props.selectedTokens.map(item => ({
          UID: item.uid,
          token: item.token,
          username: item.user || '未知用户'
        }))
      };
      responseData.value = result.data || result;
    }

    // 记录结束时间和计算总用时
    endTime.value = Date.now();
    totalDuration.value = endTime.value - startTime.value;

    // 处理结果
    if (result.success) {
      // 操作成功
      operationSuccess.value = true;
      resultDialogTitle.value = '操作结果';

      // 更新统计数据
      successCount.value = result.successCount || 0;
      failCount.value = result.failCount || props.selectedTokens.length;
      totalCount.value = result.totalCount || props.selectedTokens.length;

      // 根据成功率设置不同消息
      let baseMessage = '';
      if (result.successCount === result.totalCount) {
        baseMessage = '所有Token置入订单成功';
      } else if (result.successCount > 0) {
        baseMessage = '部分Token置入订单成功';
      } else {
        baseMessage = '没有Token置入成功';
        operationSuccess.value = false;
      }

      // 如果启用了自动删除订单，添加相关信息
      if (autoDeleteOrders.value) {
        baseMessage += '\n\n自动删除订单功能已启用，将在置入完成后执行删除操作。';
      }

      resultMessage.value = baseMessage;

      // 如果结果中包含用户分组结果，则直接使用
      if (result.userResults) {
        resultsByUser.value = result.userResults;
      } else {
        // 否则，使用原来的计算方式
        const userResults = {};

        // 初始化每个用户的成功/失败数量为0
        Object.keys(tokensByUser.value).forEach(user => {
          userResults[user] = {
            success: 0,
            fail: 0,
            total: tokensByUser.value[user]
          };
        });

        // 如果有错误数组，尝试根据用户名分配失败数量
        if (result.errorArray && result.errorArray.length > 0) {
          // 创建一个映射，根据token的UID快速查找它所属的用户
          const tokenUidToUserMap = {};
          props.selectedTokens.forEach(token => {
            tokenUidToUserMap[token.uid] = token.user || '未知用户';
          });

          // 按失败情况分配到对应用户
          if (result.failCount === props.selectedTokens.length) {
            // 如果全部失败，直接设置所有用户的token都失败
            Object.keys(userResults).forEach(user => {
              userResults[user].fail = userResults[user].total;
              userResults[user].success = 0;
            });
          } else {
            // 默认情况下，按用户的token占比分配成功和失败数量
            Object.keys(userResults).forEach(user => {
              const userTotal = userResults[user].total;
              const userFail = Math.round((userTotal / totalCount.value) * failCount.value);
              userResults[user].fail = Math.min(userFail, userTotal);
              userResults[user].success = userTotal - userResults[user].fail;
            });
          }
        } else {
          // 如果没有错误，则所有用户的token都成功了
          Object.keys(userResults).forEach(user => {
            userResults[user].success = userResults[user].total;
            userResults[user].fail = 0;
          });
        }

        // 更新按用户分组的结果
        resultsByUser.value = userResults;
      }

      // 如果勾选了自动删除订单且置入成功，则执行删除订单操作
      if (autoDeleteOrders.value && operationSuccess.value) {
        await handleAutoDeleteOrders();
      }

      // 发送成功事件
      emit('success', {
        ...result,
        successCount: successCount.value,
        failCount: failCount.value,
        totalCount: totalCount.value,
        resultsByUser: resultsByUser.value,
        startTime: startTime.value,
        endTime: endTime.value,
        totalDuration: totalDuration.value,
        deleteSuccessCount: deleteSuccessCount.value,
        deleteFailCount: deleteFailCount.value,
        deleteTotalCount: deleteTotalCount.value
      });
    } else {
      // 操作失败
      operationSuccess.value = false;
      resultDialogTitle.value = '操作结果';

      // 更新统计数据
      successCount.value = result.successCount || 0;
      failCount.value = result.failCount || props.selectedTokens.length;
      totalCount.value = result.totalCount || props.selectedTokens.length;

      // 检查是否是超时错误
      if (result.message && result.message.includes('timeout')) {
        resultMessage.value = '请求超时，可能是网络问题或服务器响应过慢。建议：\n1. 检查网络连接\n2. 减少并发数量\n3. 减少每批处理数量\n4. 稍后重试';
      } else {
        resultMessage.value = result.message || '操作失败';
      }

      // 发送错误事件
      emit('error', {
        ...result,
        successCount: successCount.value,
        failCount: failCount.value,
        totalCount: totalCount.value,
        resultsByUser: resultsByUser.value,
        startTime: startTime.value,
        endTime: endTime.value,
        totalDuration: totalDuration.value
      });
    }

    // 显示结果对话框
    resultDialogVisible.value = true;

    // 标记完成状态
    completed.value = true;
  } catch (error) {
    console.error('提交过程中发生错误:', error);
    operationSuccess.value = false;
    resultDialogTitle.value = '操作异常';

    // 记录结束时间和计算总用时
    endTime.value = Date.now();
    totalDuration.value = endTime.value - startTime.value;

    // 检查是否是超时错误
    if (error.message && error.message.includes('timeout')) {
      resultMessage.value = '请求超时，可能是网络问题或服务器响应过慢。建议：\n1. 检查网络连接\n2. 减少并发数量\n3. 减少每批处理数量\n4. 稍后重试';
    } else {
    resultMessage.value = `操作异常: ${error.message || '未知错误'}`;
    }

    resultDialogVisible.value = true;

    // 重置统计数据
    successCount.value = 0;
    failCount.value = props.selectedTokens.length;
    totalCount.value = props.selectedTokens.length;

    // 发送错误事件
    emit('error', {
      success: false,
      message: error.message,
      error,
      successCount: 0,
      failCount: props.selectedTokens.length,
      totalCount: props.selectedTokens.length,
      startTime: startTime.value,
      endTime: endTime.value,
      totalDuration: totalDuration.value
    });

    // 标记完成状态
    completed.value = true;
  } finally {
    // 关闭加载状态
    loading.value = false;
    processing.value = false;
  }
};

// 自动删除订单处理函数
const handleAutoDeleteOrders = async () => {
  try {
    // 设置删除订单状态
    deletingOrders.value = true;
    deleteProgress.value = 0;
    deleteProgressText.value = '准备删除订单...';
    processedDeleteTokens.value = 0;
    totalDeleteTokens.value = props.selectedTokens.length;

    // 重置删除统计
    deleteSuccessCount.value = 0;
    deleteFailCount.value = 0;
    deleteTotalCount.value = 0;

    console.log('开始自动删除订单，Token数量:', props.selectedTokens.length);

    // 逐个处理Token的订单删除
    for (let i = 0; i < props.selectedTokens.length; i++) {
      const token = props.selectedTokens[i];
      deleteProgressText.value = `正在删除 ${token.user || '未知用户'} 的订单...`;

      try {
        // 使用自动查询和删除订单服务
        const deleteResult = await autoQueryAndDeleteOrders(token);

        if (deleteResult.success && deleteResult.results) {
          const successCount = deleteResult.results.filter(r => r.success).length;
          const failCount = deleteResult.results.length - successCount;

          deleteSuccessCount.value += successCount;
          deleteFailCount.value += failCount;
          deleteTotalCount.value += deleteResult.results.length;

          // 更新最后删除结果
          lastDeleteResult.value = {
            username: token.user || '未知用户',
            successCount: successCount,
            failCount: failCount
          };

          console.log(`Token ${token.uid} 删除结果: 成功=${successCount}, 失败=${failCount}`);
        } else {
          // 如果没有可删除的订单，不计入失败
          console.log(`Token ${token.uid} 没有可删除的订单:`, deleteResult.message);
        }
      } catch (error) {
        deleteFailCount.value += 1;
        console.error(`Token ${token.uid} 删除异常:`, error);
      }

      // 更新进度
      processedDeleteTokens.value = i + 1;
      deleteProgress.value = Math.round((processedDeleteTokens.value / totalDeleteTokens.value) * 100);

      // 添加小延迟避免请求过于频繁
      if (i < props.selectedTokens.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    deleteProgressText.value = '删除订单完成';
    console.log('自动删除订单完成，统计:', {
      成功: deleteSuccessCount.value,
      失败: deleteFailCount.value,
      总计: deleteTotalCount.value
    });

    // 更新结果消息，包含删除订单的信息
    if (deleteTotalCount.value > 0) {
      const deleteMessage = `\n\n自动删除订单完成：共处理 ${deleteTotalCount.value} 个订单，成功删除 ${deleteSuccessCount.value} 个，失败 ${deleteFailCount.value} 个。`;
      resultMessage.value = resultMessage.value.replace('\n\n自动删除订单功能已启用，将在置入完成后执行删除操作。', deleteMessage);
    }

  } catch (error) {
    console.error('自动删除订单过程中发生错误:', error);
    ElMessage.error(`自动删除订单失败: ${error.message}`);
  } finally {
    deletingOrders.value = false;
  }
};

// 准备批次
const batches = ref([]);
const prepareBatches = (tokens, size) => {
  const userTokens = {};

  // 按用户分组
  tokens.forEach(token => {
    const username = token.user || '未知用户';
    if (!userTokens[username]) {
      userTokens[username] = [];
    }
    userTokens[username].push(token);
  });

  // 为每个用户创建批次
  let batchIndex = 0;
  const result = [];

  Object.entries(userTokens).forEach(([username, userTokensList]) => {
    // 将用户的token分成多个批次
    for (let i = 0; i < userTokensList.length; i += size) {
      const batchTokens = userTokensList.slice(i, i + size);
      result.push({
        batchIndex,
        username,
        tokens: batchTokens,
        requestData: {
          tokens: batchTokens.map(item => ({
            UID: item.uid,
            token: item.token,
            username: item.user || '未知用户'
          }))
        }
      });
      batchIndex++;
    }
  });

  return result;
};

// 显示用户详情
const showUserDetails = (user) => {
  selectedUser.value = user;
  detailsDialogVisible.value = true;

  // 显示简短的加载提示
  ElMessage({
    message: `正在加载用户 ${user} 的请求详情...`,
    type: 'info',
    duration: 1000
  });
};

// 当前选中用户的批次数据
const userBatches = computed(() => {
  if (!batchResults.value || !selectedUser.value) return [];

  return batchResults.value.filter(batch =>
    batch.username === selectedUser.value
  );
});

// 当前选中用户的批次数量
const userBatchesCount = computed(() => {
  return userBatches.value.length;
});

// 格式化的请求数据
const formattedRequestData = computed(() => {
  if (!requestData.value) return '';

  // 并发模式下，过滤显示特定用户的请求数据
  if (concurrentMode.value) {
    try {
      const userRequests = Array.isArray(requestData.value)
        ? requestData.value.filter(req => req.username === selectedUser.value)
        : [];

      return JSON.stringify(userRequests, null, 2);
    } catch (e) {
      console.error('格式化请求数据出错:', e);
      return JSON.stringify(requestData.value, null, 2);
    }
  }

  // 非并发模式下，显示完整的原始请求数据
  return JSON.stringify(requestData.value, null, 2);
});

// 格式化的响应数据
const formattedResponseData = computed(() => {
  if (!responseData.value) return '';

  // 并发模式下，过滤显示特定用户的响应数据
  if (concurrentMode.value) {
    try {
      const userResponses = Array.isArray(responseData.value)
        ? responseData.value.filter(res => res.username === selectedUser.value)
        : [];

      return JSON.stringify(userResponses, null, 2);
    } catch (e) {
      console.error('格式化响应数据出错:', e);
      return JSON.stringify(responseData.value, null, 2);
    }
  }

  // 非并发模式下，显示完整的原始响应数据
  return JSON.stringify(responseData.value, null, 2);
});

// 关闭对话框
const handleClose = () => {
  // 如果正在处理或正在删除订单，不允许关闭
  if (processing.value || deletingOrders.value) {
    ElMessage.warning('请等待操作完成后再关闭');
    return;
  }

  dialogVisible.value = false;
};

// 关闭结果对话框 - 修改为不关闭主对话框
const handleResultDialogClose = () => {
  resultDialogVisible.value = false;
  // 不再关闭主对话框
  // 保持completed状态，以便用户可以继续查看详情
};

// 批次详情对话框相关
const batchDetailsDialogVisible = ref(false);
const selectedBatch = ref(null);

// 显示批次详情
const showBatchDetails = (batch) => {
  console.log('显示批次详情:', batch);

  // 允许查看任何批次详情，不再限制未处理的批次
  // if (!batch.processed) {
  //   ElMessage.info('批次处理中，暂无详情可查看');
  //   return;
  // }

  selectedBatch.value = batch;
  batchDetailsDialogVisible.value = true;
};

// 获取批次请求数据
const formattedBatchRequestData = computed(() => {
  if (!selectedBatch.value || !selectedBatch.value.requestData) {
    return '暂无请求数据';
  }

  try {
    return JSON.stringify(selectedBatch.value.requestData, null, 2);
  } catch (e) {
    console.error('格式化批次请求数据出错:', e);
    return '数据格式化错误';
  }
});

// 获取批次响应数据
const formattedBatchResponseData = computed(() => {
  if (!selectedBatch.value || !selectedBatch.value.responseData) {
    return '暂无响应数据';
  }

  try {
    return JSON.stringify(selectedBatch.value.responseData, null, 2);
  } catch (e) {
    console.error('格式化批次响应数据出错:', e);
    return '数据格式化错误';
  }
});
</script>

<style scoped>
.token-order-content {
  padding: 20px;
}

.info-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.selected-tokens-info {
  margin: 15px 0;
  font-size: 16px;
}

.user-stats-section {
  margin: 20px 0;
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.user-stats-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #409EFF;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.expand-toggle {
  cursor: pointer;
  font-size: 14px;
  font-weight: normal;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.expand-toggle:hover {
  color: #409EFF;
  background: #ecf5ff;
}

.collapsed-info {
  text-align: center;
  margin-top: 10px;
}

.user-stats-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.user-stats-item {
  display: flex;
  flex-direction: column;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
  transition: all 0.3s;
}

.user-stats-item:hover {
  background: #ecf5ff;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.label {
  color: #606266;
  font-size: 14px;
}

.user-count {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-progress {
  width: 150px;
  margin-top: 5px;
}

.concurrent-settings {
  margin: 20px 0;
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

/* 自动删除订单设置样式 */
.auto-delete-settings {
  margin: 20px 0;
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  border-left: 4px solid #E6A23C;
}

.auto-delete-form {
  margin-top: 10px;
}

.auto-delete-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  font-weight: 500;
  color: #606266;
}

.auto-delete-description {
  margin-left: 24px;
  padding: 8px 12px;
  background: #FDF6EC;
  border-radius: 4px;
  border-left: 3px solid #E6A23C;
}

.settings-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #409EFF;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.setting-label {
  width: 120px;
  color: #606266;
}

.progress-container {
  margin: 20px 0;
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.progress-title {
  font-size: 16px;
  margin-bottom: 10px;
  color: #409EFF;
}

.progress-details {
  margin-top: 10px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 6px;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 删除订单进度样式 */
.delete-progress-container {
  margin: 20px 0;
  background: #FEF7E6;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  border-left: 4px solid #E6A23C;
}

.delete-progress-title {
  font-size: 16px;
  font-weight: bold;
  color: #E6A23C;
  margin-bottom: 10px;
}

.delete-progress-details {
  margin-top: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
}

.delete-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.result-content {
  text-align: center;
  padding: 20px;
}

.result-icon-container {
  margin-bottom: 20px;
}

.result-icon {
  font-size: 48px;
}

.success-icon {
  color: #67C23A;
}

.error-icon {
  color: #F56C6C;
}

.result-message {
  font-size: 18px;
  margin-bottom: 20px;
  white-space: pre-line;
}

.result-statistics {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 25px 0;
}

.stat-section {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 15px;
}

.stat-section-title {
  font-size: 16px;
  font-weight: bold;
  color: #606266;
  margin-bottom: 15px;
  text-align: center;
}

.stat-cards {
  display: flex;
  justify-content: space-around;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 25px;
  background: #f5f7fa;
  border-radius: 8px;
  min-width: 80px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
}

.stat-value.total {
  color: #409EFF;
}

.stat-value.success {
  color: #67C23A;
}

.stat-value.fail {
  color: #F56C6C;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.time-statistics {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: left;
}

.time-item {
  margin: 8px 0;
  display: flex;
  justify-content: space-between;
}

.time-label {
  font-weight: bold;
  color: #606266;
}

.time-value {
  color: #409EFF;
}

.total-time {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px dashed #DCDFE6;
}

.total-time .time-value {
  font-weight: bold;
  font-size: 16px;
  color: #F56C6C;
}

.batch-stats-summary {
  background: #f0f9eb;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: left;
}

.summary-title {
  font-weight: bold;
  color: #67C23A;
  margin-bottom: 5px;
}

.summary-content {
  color: #606266;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #409EFF;
  text-align: left;
}

.user-results-section {
  margin: 20px 0;
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.user-results-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.user-result-item {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s;
}

.user-result-item:hover {
  background: #ecf5ff;
  transform: translateX(5px);
}

.user-result-header {
  margin-bottom: 10px;
}

.user-result-stats {
  display: flex;
  justify-content: space-around;
  background: white;
  padding: 10px;
  border-radius: 6px;
}

.user-result-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.clickable-tag:hover {
  transform: scale(1.05);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.progress-details-content {
  padding: 20px;
}

.progress-overview {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.progress-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 6px;
  min-width: 80px;
}

.batch-list-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #409EFF;
}

.user-batch-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 350px;
  overflow-y: auto;
}

.user-batch-item {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.3s;
  cursor: pointer;
  margin-bottom: 10px;
}

.user-batch-item:hover {
  background: #ecf5ff;
  transform: translateY(-2px);
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.batch-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 10px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  padding: 8px;
}

.batch-detail-item {
  background: white;
  padding: 6px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  color: #909399;
  font-size: 12px;
}

.detail-value {
  font-weight: bold;
}

.detail-value.success {
  color: #67C23A;
}

.detail-value.fail {
  color: #F56C6C;
}

.batch-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10px;
  background: #f0f7ff;
  padding: 10px;
  border-radius: 6px;
}

.processing-text {
  font-size: 12px;
  color: #409EFF;
  margin-top: 5px;
}

.no-batch-data {
  text-align: center;
  padding: 30px 0;
  background: #f9f9f9;
  border-radius: 8px;
  margin-top: 15px;
}

.processing-animation {
  display: flex;
  gap: 6px;
}

.processing-animation span {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #409EFF;
  border-radius: 50%;
  animation: bounce 1s infinite ease-in-out;
}

.processing-animation span:nth-child(1) {
  animation-delay: 0s;
}

.processing-animation span:nth-child(2) {
  animation-delay: 0.2s;
}

.processing-animation span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.batch-details-content {
  padding: 20px;
}

.batch-overview {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 15px;
}

.batch-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.batch-info-label {
  font-size: 14px;
  color: #909399;
}

.batch-info-value {
  font-weight: bold;
  font-size: 14px;
  color: #606266;
}

.batch-info-value.success {
  color: #67C23A;
}

.batch-info-value.fail {
  color: #F56C6C;
}

.batch-details-tabs {
  margin-top: 20px;
}

.batch-data-section {
  padding: 10px;
}

.batch-data-section h3 {
  margin-bottom: 15px;
  color: #409EFF;
  font-size: 16px;
}
</style>